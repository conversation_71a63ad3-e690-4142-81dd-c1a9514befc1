---
title: iOS 快速开始
description: 快速搭建和运行 AppSolve iOS 应用
---

# iOS 快速开始

本指南将帮助你快速搭建和运行 AppSolve iOS 应用。AppSolve 是一个功能强大的 AI 内容生成应用模板，支持图像生成、编辑、订阅管理等功能。

## 前提条件

在开始之前，请确保你的开发环境满足以下要求：

- **macOS** 13.0 或更高版本
- **Xcode** 15.0 或更高版本
- **iOS** 16.0+ 部署目标
- **Swift** 5.0+
- 有效的 **Apple Developer Account**
- **Firebase** 项目（用于认证、数据库和存储）
- **RevenueCat** 账户（用于订阅管理）

## 步骤 1：克隆项目

```bash
# 克隆仓库
git clone https://github.com/your-org/appsolve-ios.git
cd appsolve-ios
```

## 步骤 2：安装依赖

本项目使用 Swift Package Manager (SPM) 管理依赖。打开 Xcode 后，依赖会自动下载。

主要依赖包括：
- Firebase（认证、Firestore、存储、分析）
- RevenueCat（订阅管理）
- Google Sign-In（社交登录）
- Sentry（错误监控）
- ExyteChat（聊天界面）

## 步骤 3：配置 Firebase

### 3.1 创建 Firebase 项目

1. 访问 [Firebase Console](https://console.firebase.google.com)
2. 创建新项目或使用现有项目
3. 添加 iOS 应用，Bundle ID 为：`com.yourcompany.ChatToDesign`

### 3.2 下载配置文件

1. 下载 `GoogleService-Info.plist`
2. 将文件拖拽到 Xcode 项目的 `ChatToDesign` 目录
3. 确保文件被添加到正确的 target

### 3.3 启用 Firebase 服务

在 Firebase Console 中启用以下服务：
- **Authentication**：启用 Email/Password 和 Google Sign-In
- **Firestore Database**：创建数据库（建议从生产模式开始）
- **Storage**：创建默认存储桶
- **Analytics**：自动启用

## 步骤 4：配置 RevenueCat

### 4.1 创建 RevenueCat 应用

1. 登录 [RevenueCat Dashboard](https://app.revenuecat.com)
2. 创建新应用
3. 添加 App Store 应用信息

### 4.2 配置 API 密钥

1. 获取 RevenueCat API Key
2. 在项目中找到 `Constants.swift` 文件
3. 更新 RevenueCat 配置：

```swift
// Utils/Constants.swift
struct Constants {
    static let revenueCatAPIKey = "your_revenuecat_api_key"
    // 其他配置...
}
```

## 步骤 5：配置 API 端点

更新后端 API 端点配置：

```swift
// Utils/Constants.swift
struct Constants {
    static let apiBaseURL = "https://your-backend-domain.com"
    // 或本地开发
    // static let apiBaseURL = "http://localhost:8787"
}
```

## 步骤 6：运行项目

### 6.1 打开项目

```bash
open ChatToDesign.xcodeproj
```

### 6.2 选择开发团队

1. 在 Xcode 中选择项目文件
2. 选择 "Signing & Capabilities" 标签
3. 选择你的开发团队

### 6.3 运行应用

1. 选择目标设备（模拟器或真机）
2. 按下 `Cmd + R` 或点击运行按钮
3. 等待构建完成并启动应用

## 步骤 7：测试核心功能

应用启动后，测试以下核心功能：

1. **用户认证**
   - 注册新账户
   - 使用 Google 登录
   - 登出功能

2. **内容生成**
   - 创建图像生成任务
   - 查看生成历史
   - 编辑和保存图像

3. **订阅功能**
   - 查看订阅计划
   - 测试购买流程（使用沙盒环境）

## 常见问题

### 构建失败

1. **依赖下载失败**
   - 清理构建：`Cmd + Shift + K`
   - 重置包缓存：`File > Packages > Reset Package Caches`

2. **签名问题**
   - 确保选择了正确的开发团队
   - 检查 Bundle ID 是否唯一

### Firebase 连接问题

1. 确认 `GoogleService-Info.plist` 已正确添加
2. 检查 Firebase 项目配置是否匹配
3. 确保网络连接正常

### RevenueCat 问题

1. 验证 API Key 是否正确
2. 确保产品 ID 在 App Store Connect 中已创建
3. 使用沙盒账户测试购买

## 下一步

- 查看 [iOS 配置指南](./configuration-ios.zh.mdx) 了解详细配置选项
- 阅读 [后端快速开始](./quick-start-backend.zh.mdx) 搭建后端服务
- 探索项目架构和自定义开发

## 获取帮助

如果遇到问题：
1. 查看项目中的 `Documentation` 目录
2. 检查 Firebase 和 RevenueCat 的官方文档
3. 在项目 Issues 中搜索或提交问题