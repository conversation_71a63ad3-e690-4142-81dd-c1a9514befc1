// @ts-nocheck -- skip type checking
import * as docs_4 from "../content/docs/quick-start-ios.zh.mdx?collection=docs&hash=1752567730927"
import * as docs_3 from "../content/docs/quick-start-backend.zh.mdx?collection=docs&hash=1752567730927"
import * as docs_2 from "../content/docs/overview.mdx?collection=docs&hash=1752567730927"
import * as docs_1 from "../content/docs/configuration-ios.zh.mdx?collection=docs&hash=1752567730927"
import * as docs_0 from "../content/docs/configuration-backend.zh.mdx?collection=docs&hash=1752567730927"
import { _runtime } from "fumadocs-mdx"
import * as _source from "../source.config"
export const docs = _runtime.docs<typeof _source.docs>([{ info: {"path":"configuration-backend.zh.mdx","absolutePath":"/Users/<USER>/Workspace/project/a1d-app/shipany-template/content/docs/configuration-backend.zh.mdx"}, data: docs_0 }, { info: {"path":"configuration-ios.zh.mdx","absolutePath":"/Users/<USER>/Workspace/project/a1d-app/shipany-template/content/docs/configuration-ios.zh.mdx"}, data: docs_1 }, { info: {"path":"overview.mdx","absolutePath":"/Users/<USER>/Workspace/project/a1d-app/shipany-template/content/docs/overview.mdx"}, data: docs_2 }, { info: {"path":"quick-start-backend.zh.mdx","absolutePath":"/Users/<USER>/Workspace/project/a1d-app/shipany-template/content/docs/quick-start-backend.zh.mdx"}, data: docs_3 }, { info: {"path":"quick-start-ios.zh.mdx","absolutePath":"/Users/<USER>/Workspace/project/a1d-app/shipany-template/content/docs/quick-start-ios.zh.mdx"}, data: docs_4 }], [{"info":{"path":"meta.json","absolutePath":"/Users/<USER>/Workspace/project/a1d-app/shipany-template/content/docs/meta.json"},"data":{"pages":["overview","getting-started","firebase-setup","authentication","subscriptions","backend-setup","configuration","testing","deployment","troubleshooting"]}}, {"info":{"path":"meta.zh.json","absolutePath":"/Users/<USER>/Workspace/project/a1d-app/shipany-template/content/docs/meta.zh.json"},"data":{"pages":["overview","quick-start-ios","quick-start-backend","configuration-ios","configuration-backend","getting-started","firebase-setup","authentication","subscriptions","backend-setup","configuration","testing","deployment","troubleshooting"]}}])